package com.cmpay.payment.constant;


/**
 * Create on 2018/11/02
 *
 * <AUTHOR>
 */

public enum PaymentWayEnum{
	/**
	 * 和包
	 */
	CMPAY("和包"),
	/**
	 * 支付宝
	 */
	ALIPAY("支付宝"),
	/**
	 * 微信
	 */
	WECHAT("微信"),
	/**
	 * 银联
	 */
	UNIONPAY("银联云闪付"),
	/**
	 * 网银
	 */
	NETBANK("网银支付"),
	/**
	 * 工行
	 */
	ICBCPAY("工行"),
	/**
	 * 积分商城
	 */
	INTEGRALPAY("积分商城"),
	/**
	 * 招行一网通
	 */
	CMBAION("招行一网通"),
	/**
	 * 数币平台
	 */
	DCEPPAY("数币平台");
	private String desc;

	PaymentWayEnum(String desc) {
		this.desc = desc;
	}

	public String getDesc() {
		return desc;
	}
}
