package com.cmpay.payment.service.base.impl;

import com.alibaba.fastjson.JSONObject;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.common.utils.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;

import com.cmpay.payment.bo.TimeRangeResultBO;
import com.cmpay.payment.constant.AppEnum;
import com.cmpay.payment.constant.OrderStatusEnum;
import com.cmpay.payment.constant.PaymentSceneEnum;
import com.cmpay.payment.constant.PaymentWayEnum;
import com.cmpay.payment.schedule.bo.config.SubMerchantBO;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.schedule.bo.notify.TradeOrderAndNotifyBO;
import com.cmpay.payment.schedule.bo.pay.FapOrderQueryBO;
import com.cmpay.payment.schedule.bo.pay.PaymentQueryBO;
import com.cmpay.payment.schedule.bo.settlement.TradeSettlementBO;
import com.cmpay.payment.schedule.bo.settlement.TradeSettlementQueryBO;
import com.cmpay.payment.schedule.bo.withhold.ContractQueryBO;
import com.cmpay.payment.schedule.bo.withhold.ContractWithholdQueryBO;
import com.cmpay.payment.entity.TradeOrderDO;
import com.cmpay.payment.schedule.entity.pay.OrderPaymentAttachDO;
import com.cmpay.payment.schedule.entity.withhold.ContractDeleteDO;
import com.cmpay.payment.schedule.entity.withhold.ContractWithholdDO;
import com.cmpay.payment.schedule.service.config.IExtSubMerchantService;
import com.cmpay.payment.schedule.service.ext.async.AsynCommonService;
import com.cmpay.payment.schedule.service.pay.ExtPayOrderAttachService;
import com.cmpay.payment.schedule.service.pay.ExtPayOrderService;
import com.cmpay.payment.schedule.service.settlement.IExtTradeSettlementService;
import com.cmpay.payment.schedule.service.withhold.IExtContractDeleteService;
import com.cmpay.payment.schedule.service.withhold.IExtContractWithholdService;
import com.cmpay.payment.service.base.ICommonManagerDataService;
import com.cmpay.payment.service.contract.IContractSynService;
import com.cmpay.payment.service.pay.TradeSynService;
import com.cmpay.payment.service.upcc.MixPayMasterOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * Created on 2020/8/6
 */
@Service
public class CommonManagerDataServiceImpl implements ICommonManagerDataService {

    private static final Logger logger = LoggerFactory.getLogger(CommonManagerDataServiceImpl.class);

    @Autowired
    private ExtPayOrderService payOrderService;
    @Autowired
    private TradeSynService tradeSynService;
    @Autowired
    private IExtContractDeleteService contractDeleteService;
    @Autowired
    private IContractSynService contractSynService;
    @Autowired
    private IExtContractWithholdService contractWithholdService;
    @Autowired
    private AsynCommonService asynCommonService;
    @Autowired
    private ExtPayOrderAttachService payOrderAttachService;

    @Value("${test.merchant:}")
    private String testMerchant;
    @Autowired
    private IExtSubMerchantService subMerchantService;
    @Autowired
    IExtTradeSettlementService settlementService;
    @Autowired
    private MixPayMasterOrderService masterOrderService;

    private static final String TOTAL = "2000";
    public static final String FAIL_MSG = "参数有误";

    @Override
    public ReturnT<String> managerData(String channel, int shardIndex, int shardTotal, String param, String dataScope) {
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        String logInfo = channel + "queryAllPayOrder" + DateTimeUtils.formatLocalDateTime(localDateTime);
        logger.info("start {}", logInfo);
        if (!dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        try {
            FapOrderQueryBO queryBO = new FapOrderQueryBO();
            LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
            queryBO.setOrderDate(DateTimeUtils.formatLocalDateTime(localDateTime.minusSeconds(40)));
            queryBO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
            queryBO.setOrderTime(DateTimeUtils.formatLocalDateTime(localDateTime.minusHours(2)));
            queryBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
            queryBO.setPayProductCode(channel);
            queryBO.setTotal(StringUtils.isNotBlank(param) ? param : TOTAL);
            queryBO.setCurrentIndex(shardIndex);
            queryBO.setTotalSelect(shardTotal - 1);
            queryBO.setDataCenter(dataScope);
            List<TradeOrderDO> tradeOrderList = payOrderService.queryWaitPaymentChannelList(queryBO);
            if (tradeOrderList == null || tradeOrderList.size() == 0) {
                logger.info("{},Payment Order is Empty!!, end {}", queryBO.getPayProductCode(), logInfo);
                return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
            }
            logger.info("queryAllPayOrder");
            sendOrderData(tradeOrderList, shardIndex);
            logger.info("end {}", logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                logger.info("order handle fail:{}", e.getMessage());
            } else {
                logger.error("order handle fail:{}", e);
            }
            return ReturnT.FAIL;
        }
    }

    @Override
    public ReturnT<String> managerDataScanBarcode(String channel, int shardIndex, int shardTotal, String param, String dataScope) {
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        String logInfo = channel + "syncBarcodeMicroPayOrder" + DateTimeUtils.formatLocalDateTime(localDateTime);
        logger.info("start {}", logInfo);
        if (!dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        try {
            FapOrderQueryBO queryBO = new FapOrderQueryBO();
            LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
            queryBO.setOrderDate(DateTimeUtils.formatLocalDateTime(localDateTime.minusSeconds(5)));
            queryBO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
            queryBO.setOrderTime(DateTimeUtils.formatLocalDateTime(localDateTime.minusMinutes(30)));
            queryBO.setAccountDate(DateTimeUtils.getCurrentDateStr());
            queryBO.setPayProductCode(channel);
            queryBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
            queryBO.setTotal(StringUtils.isNotBlank(param) ? param : TOTAL);
            queryBO.setCurrentIndex(shardIndex);
            queryBO.setTotalSelect(shardTotal - 1);
            queryBO.setDataCenter(dataScope);
            List<TradeOrderDO> tradeOrderList = payOrderService.queryOrderScanBarcodeList(queryBO);
            if (tradeOrderList.size() == 0) {
                logger.info("{},Scan/Barcode Payment Order is Empty!!", queryBO.getPayProductCode());
                logger.info("end {}", logInfo);
                return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
            }
            logger.info("syncBarcodeMicroPayOrder");
            sendOrderData(tradeOrderList, shardIndex);
            logger.info("end {}", logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                logger.info("order handle fail:{}", e.getMessage());
            } else {
                logger.error("order handle fail:{}", e);
            }
            return ReturnT.FAIL;
        }
    }

    /**
     * 数据发送上游
     *
     * @param tradeOrderList
     * @param shardIndex
     */
    private void sendOrderData(List<TradeOrderDO> tradeOrderList, int shardIndex) {
        for (TradeOrderDO tradeOrder : tradeOrderList) {
            logger.info("当前分片: {}, 处理对象: {}", shardIndex, tradeOrder.getOutTradeNo());
            //支付宝，微信的签约代扣走单独的接口查询
            if (StringUtils.equalsAnyIgnoreCase(tradeOrder.getAimProductCode(), PaymentWayEnum.ALIPAY.name(), PaymentWayEnum.WECHAT.name())
                    && (StringUtils.equalsIgnoreCase(tradeOrder.getPayWayCode(), PaymentSceneEnum.CONTRACTPAY.name()))) {
                return;
            }
            PaymentQueryBO queryBO = new PaymentQueryBO();
            dealDcepAttachInfo(tradeOrder,queryBO);
            queryBO.setPaymentOrder(tradeOrder);
            try {
                queryBO.setSourceApp(AppEnum.integrationshecdule.name());
                TradeOrderAndNotifyBO orderAndNotifyBO = tradeSynService.paymentOrderSyn(queryBO);
                // 当orderAndNotifyBO为空时，查询下一笔订单
                if (JudgeUtils.isNull(orderAndNotifyBO)) {
                    continue;
                }
                logger.info("tradeOrderDO:{}", queryBO.getPaymentOrder().toString());
                // 新增签约扣款失败订单通知
                if (StringUtils.equals(queryBO.getPaymentOrder().getStatus(), OrderStatusEnum.TRADE_SUCCESS.name())
                        || (JudgeUtils.equalsIgnoreCase(tradeOrder.getPayWayCode(), PaymentSceneEnum.CONTRACTPAY.name())
                        && JudgeUtils.notEquals(tradeOrder.getStatus(), OrderStatusEnum.WAIT_PAY.name())
                )) {
                    asynCommonService.asyncNotify(orderAndNotifyBO.getTradeNotifyBO());
                }
            } catch (Exception e) {
                if (e instanceof BusinessException) {
                    logger.info("orderInfo:{},error message: {}", queryBO.getPaymentOrder().getTradeOrderNo(), e.getMessage());
                } else {
                    logger.error("orderInfo:{},error message: {}", queryBO.getPaymentOrder().getTradeOrderNo(), e);
                }
            }
        }
    }

    private void dealDcepAttachInfo(TradeOrderDO tradeOrder, PaymentQueryBO queryBO) {
        if (StringUtils.equalsAnyIgnoreCase(tradeOrder.getAimProductCode(), PaymentWayEnum.ICBCPAY.name(),PaymentWayEnum.DCEPPAY.name())){
            OrderPaymentAttachDO attachDO = new OrderPaymentAttachDO();
            attachDO.setOrderDate(tradeOrder.getRequestDate());
            attachDO.setOutTradeNo(tradeOrder.getOutTradeNo());
            OrderPaymentAttachDO orderPaymentAttachDO = payOrderAttachService.load(attachDO);
            if (JudgeUtils.isNull(orderPaymentAttachDO)) {
                return;
            }
            if (JudgeUtils.isEmpty(orderPaymentAttachDO.getAgreementId()) &&JudgeUtils.isEmpty(orderPaymentAttachDO.getSubMerchantId()) ) {
                return ;
            }
            queryBO.setOrderPaymentAttachDO(orderPaymentAttachDO);
        }
    }

    @Override
    public boolean dataInspect(int shardIndex, int shardTotal) {
        logger.info("分片参数：当前分片序号 = {}, 总分片数 = {}", shardIndex, shardTotal);
        if (shardTotal == 0) {
            logger.info("总分片:{}", shardTotal);
            return false;
        }
        return true;
    }

    @Override
    public ReturnT<String> managerDataFirst(String channel, int shardIndex, int shardTotal, String param, String dataScope) {
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        String logInfo = channel + "queryAllPayFirstOrder" + DateTimeUtils.formatLocalDateTime(localDateTime);
        logger.info("start {}", logInfo);
        if (!dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        try {
            FapOrderQueryBO queryBO = new FapOrderQueryBO();
            LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
            queryBO.setOrderDate(DateTimeUtils.formatLocalDateTime(localDateTime.minusSeconds(5)));
            queryBO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
            queryBO.setOrderTime(DateTimeUtils.formatLocalDateTime(localDateTime.minusMinutes(1)));
            queryBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
            queryBO.setPayProductCode(channel);
            queryBO.setTotal(StringUtils.isNotBlank(param) ? param : TOTAL);
            queryBO.setCurrentIndex(shardIndex);
            queryBO.setTotalSelect(shardTotal - 1);
            queryBO.setDataCenter(dataScope);
            List<TradeOrderDO> tradeOrderList = payOrderService.queryWaitPaymentChannelList(queryBO);
            if (tradeOrderList == null || tradeOrderList.size() == 0) {
                logger.info("{},Payment Order First is Empty!!", queryBO.getPayProductCode());
                logger.info("end {}", logInfo);
                return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
            }
            logger.info("{} queryAllPayFirstOrder", queryBO.getPayProductCode());
            sendOrderData(tradeOrderList, shardIndex);
            logger.info("end {}", logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                logger.info("order handle fail:{}", e.getMessage());
            } else {
                logger.error("order handle fail:{}", e);
            }
            return ReturnT.FAIL;
        }
    }

    @Override
    public ReturnT<String> managerDataSecond(String channel, int shardIndex, int shardTotal, String param, String dataScope) {
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        String logInfo = channel + "queryAllPaySecondOrder" + DateTimeUtils.formatLocalDateTime(localDateTime);
        logger.info("start {}", logInfo);
        if (!dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        try {
            FapOrderQueryBO queryBO = new FapOrderQueryBO();
            LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
            queryBO.setOrderDate(DateTimeUtils.formatLocalDateTime(localDateTime.minusMinutes(1)));
            queryBO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
            queryBO.setOrderTime(DateTimeUtils.formatLocalDateTime(localDateTime.minusMinutes(5)));
            queryBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
            queryBO.setPayProductCode(channel);
            queryBO.setTotal(StringUtils.isNotBlank(param) ? param : TOTAL);
            queryBO.setCurrentIndex(shardIndex);
            queryBO.setTotalSelect(shardTotal - 1);
            queryBO.setDataCenter(dataScope);
            List<TradeOrderDO> tradeOrderList = payOrderService.queryWaitPaymentChannelList(queryBO);
            if (tradeOrderList == null || tradeOrderList.size() == 0) {
                logger.info("{},Payment Order Second is Empty!!", queryBO.getPayProductCode());
                logger.info("end {}", logInfo);
                return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
            }
            logger.info("{} queryAllPaySecondOrder", queryBO.getPayProductCode());
            sendOrderData(tradeOrderList, shardIndex);
            logger.info("end {}", logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                logger.info("order handle fail:{}", e.getMessage());
            } else {
                logger.error("order handle fail:{}", e);
            }
            return ReturnT.FAIL;
        }
    }

    @Override
    public ReturnT<String> managerDataThird(String channel, int shardIndex, int shardTotal, String param, String dataScope) {
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        String logInfo = channel + "queryAllPayThirdOrder" + DateTimeUtils.formatLocalDateTime(localDateTime);
        logger.info("start {}", logInfo);
        if (!dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        try {
            FapOrderQueryBO queryBO = new FapOrderQueryBO();
            LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
            queryBO.setOrderDate(DateTimeUtils.formatLocalDateTime(localDateTime.minusMinutes(5)));
            queryBO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
            queryBO.setOrderTime(DateTimeUtils.formatLocalDateTime(localDateTime.minusMinutes(30)));
            queryBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
            queryBO.setPayProductCode(channel);
            queryBO.setTotal(StringUtils.isNotBlank(param) ? param : TOTAL);
            queryBO.setCurrentIndex(shardIndex);
            queryBO.setTotalSelect(shardTotal - 1);
            queryBO.setDataCenter(dataScope);
            List<TradeOrderDO> tradeOrderList = payOrderService.queryWaitPaymentChannelList(queryBO);
            if (tradeOrderList == null || tradeOrderList.size() == 0) {
                logger.info("{},Payment Order Third is Empty!!", queryBO.getPayProductCode());
                logger.info("end {}", logInfo);
                return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
            }
            logger.info("{} queryAllPayThirdOrder", queryBO.getPayProductCode());
            sendOrderData(tradeOrderList, shardIndex);
            logger.info("end {}", logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                logger.info("order handle fail:{}", e.getMessage());
            } else {
                logger.error("order handle fail:{}", e);
            }
            return ReturnT.FAIL;
        }
    }

    @Override
    public ReturnT<String> managerDataFour(String channel, int shardIndex, int shardTotal, String param, String dataScope) {
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        String logInfo = channel + "queryAllPayFourOrder" + DateTimeUtils.formatLocalDateTime(localDateTime);
        logger.info("start {}", logInfo);
        if (!dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        try {
            FapOrderQueryBO queryBO = new FapOrderQueryBO();
            LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
            queryBO.setOrderDate(DateTimeUtils.formatLocalDateTime(localDateTime.minusMinutes(30)));
            queryBO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
            queryBO.setOrderTime(DateTimeUtils.formatLocalDateTime(localDateTime.minusHours(2)));
            queryBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
            queryBO.setPayProductCode(channel);
            queryBO.setTotal(StringUtils.isNotBlank(param) ? param : TOTAL);
            queryBO.setCurrentIndex(shardIndex);
            queryBO.setTotalSelect(shardTotal - 1);
            queryBO.setDataCenter(dataScope);
            List<TradeOrderDO> tradeOrderList = payOrderService.queryWaitPaymentChannelList(queryBO);
            if (tradeOrderList == null || tradeOrderList.size() == 0) {
                logger.info("{},Payment Order Four is Empty!!", queryBO.getPayProductCode());
                logger.info("end {}", logInfo);
                return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
            }
            logger.info("{} queryAllPayFourOrder", queryBO.getPayProductCode());
            sendOrderData(tradeOrderList, shardIndex);
            logger.info("end {}", logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                logger.info("order handle fail:{}", e.getMessage());
            } else {
                logger.error("order handle fail:{}", e);
            }
            return ReturnT.FAIL;
        }
    }

    @Override
    public ReturnT<String> managerDataScanBarcodeFirst(String channel, int shardIndex, int shardTotal, String param, String dataScope) {
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        String logInfo = channel + "syncBarcodeMicroPayFirstOrder" + DateTimeUtils.formatLocalDateTime(localDateTime);
        logger.info("start {}", logInfo);
        if (!dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        try {
            FapOrderQueryBO queryBO = new FapOrderQueryBO();
            LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
            queryBO.setOrderDate(DateTimeUtils.formatLocalDateTime(localDateTime.minusSeconds(5)));
            queryBO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
            queryBO.setOrderTime(DateTimeUtils.formatLocalDateTime(localDateTime.minusMinutes(1)));
            queryBO.setAccountDate(DateTimeUtils.getCurrentDateStr());
            queryBO.setPayProductCode(channel);
            queryBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
            queryBO.setTotal(StringUtils.isNotBlank(param) ? param : TOTAL);
            queryBO.setCurrentIndex(shardIndex);
            queryBO.setTotalSelect(shardTotal - 1);
            queryBO.setDataCenter(dataScope);
            List<TradeOrderDO> tradeOrderList = payOrderService.queryOrderScanBarcodeList(queryBO);
            if (tradeOrderList.size() == 0) {
                logger.info("{},Scan/Barcode Payment Order First is Empty!!", queryBO.getPayProductCode());
                logger.info("end {}", logInfo);
                return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
            }
            logger.info("{} syncBarcodeMicroPayFirstOrder", queryBO.getPayProductCode());
            sendOrderData(tradeOrderList, shardIndex);
            logger.info("end {}", logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                logger.info("order handle fail:{}", e.getMessage());
            } else {
                logger.error("order handle fail:{}", e);
            }
            return ReturnT.FAIL;
        }
    }

    @Override
    public ReturnT<String> managerDataScanBarcodeSecond(String channel, int shardIndex, int shardTotal, String param, String dataScope) {
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        String logInfo = channel + "syncBarcodeMicroPaySecondOrder" + DateTimeUtils.formatLocalDateTime(localDateTime);
        logger.info("start {}", logInfo);
        if (!dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        try {
            FapOrderQueryBO queryBO = new FapOrderQueryBO();
            LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
            queryBO.setOrderDate(DateTimeUtils.formatLocalDateTime(localDateTime.minusMinutes(1)));
            queryBO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
            queryBO.setOrderTime(DateTimeUtils.formatLocalDateTime(localDateTime.minusMinutes(5)));
            queryBO.setAccountDate(DateTimeUtils.getCurrentDateStr());
            queryBO.setPayProductCode(channel);
            queryBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
            queryBO.setTotal(StringUtils.isNotBlank(param) ? param : TOTAL);
            queryBO.setCurrentIndex(shardIndex);
            queryBO.setTotalSelect(shardTotal - 1);
            queryBO.setDataCenter(dataScope);
            List<TradeOrderDO> tradeOrderList = payOrderService.queryOrderScanBarcodeList(queryBO);
            if (tradeOrderList.size() == 0) {
                logger.info("{},Scan/Barcode Payment Order Second is Empty!!", queryBO.getPayProductCode());
                logger.info("end {}", logInfo);
                return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
            }
            logger.info("{} syncBarcodeMicroPaySecondOrder", queryBO.getPayProductCode());
            sendOrderData(tradeOrderList, shardIndex);
            logger.info("end {}", logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                logger.info("order handle fail:{}", e.getMessage());
            } else {
                logger.error("order handle fail:{}", e);
            }
            return ReturnT.FAIL;
        }
    }

    @Override
    public ReturnT<String> managerDataScanBarcodeThird(String channel, int shardIndex, int shardTotal, String param, String dataScope) {
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        String logInfo = channel + "syncBarcodeMicroPayThirdOrder" + DateTimeUtils.formatLocalDateTime(localDateTime);
        logger.info("start {}", logInfo);
        if (!dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        try {
            FapOrderQueryBO queryBO = new FapOrderQueryBO();
            LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
            queryBO.setOrderDate(DateTimeUtils.formatLocalDateTime(localDateTime.minusMinutes(5)));
            queryBO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
            queryBO.setOrderTime(DateTimeUtils.formatLocalDateTime(localDateTime.minusMinutes(30)));
            queryBO.setAccountDate(DateTimeUtils.getCurrentDateStr());
            queryBO.setPayProductCode(channel);
            queryBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
            queryBO.setTotal(StringUtils.isNotBlank(param) ? param : TOTAL);
            queryBO.setCurrentIndex(shardIndex);
            queryBO.setTotalSelect(shardTotal - 1);
            queryBO.setDataCenter(dataScope);
            List<TradeOrderDO> tradeOrderList = payOrderService.queryOrderScanBarcodeList(queryBO);
            if (tradeOrderList.size() == 0) {
                logger.info("{},Scan/Barcode Payment Order Third is Empty!!", queryBO.getPayProductCode());
                logger.info("end {}", logInfo);
                return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
            }
            logger.info("{} syncBarcodeMicroPayThirdOrder", queryBO.getPayProductCode());
            sendOrderData(tradeOrderList, shardIndex);
            logger.info("end {}", logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                logger.info("order handle fail:{}", e.getMessage());
            } else {
                logger.error("order handle fail:{}", e);
            }
            return ReturnT.FAIL;
        }
    }

    @Override
    public ReturnT<String> allTestOrderSyn(int shardIndex, int shardTotal, String param, String dataScope) {
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        String logInfo = "allTestOrderSyn" + DateTimeUtils.formatLocalDateTime(localDateTime);
        logger.info("start {}", logInfo);
        if (!dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        try {
            FapOrderQueryBO queryBO = new FapOrderQueryBO();
            LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
            queryBO.setOrderDate(DateTimeUtils.formatLocalDateTime(localDateTime.minusMinutes(1)));
            queryBO.setRequestDate(DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
            queryBO.setOrderTime(DateTimeUtils.formatLocalDateTime(localDateTime.minusMinutes(30)));
            queryBO.setStatus(OrderStatusEnum.WAIT_PAY.name());
            queryBO.setTotal(StringUtils.isNotBlank(param) ? param : TOTAL);
            queryBO.setTotalSelect(shardTotal - 1);
            queryBO.setCurrentIndex(shardIndex);
            queryBO.setMerchantNo(testMerchant);
            queryBO.setDataCenter(dataScope);
            List<TradeOrderDO> tradeOrderList = payOrderService.queryWaitPaymentChannelTestList(queryBO);
            if (tradeOrderList == null || tradeOrderList.size() == 0) {
                logger.info("{},Payment Order is Empty!!, end {}", queryBO.getPayProductCode(), logInfo);
                return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
            }
            logger.info("queryAllPayOrder");
            sendOrderData(tradeOrderList, shardIndex);
            logger.info("end {}", logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                logger.info("order handle fail:{}", e.getMessage());
            } else {
                logger.error("order handle fail:{}", e);
            }
            return ReturnT.FAIL;
        }
    }

    @Override
    public ReturnT<String> standbyOrderSyn(String param, int shardIndex, int shardTotal, String dataScope) {
        logger.info("standbyOrderSyn is start {}", DateTimeUtils.getCurrentDateTimeStr());
        FapOrderQueryBO queryBO = new FapOrderQueryBO();
        try {
            // 参数校验
            if (!checkParams(queryBO, param)) {
                logger.info("params is error,standbyOrderSyn is end {}", DateTimeUtils.getCurrentDateTimeStr());
                return new ReturnT(ReturnT.FAIL_CODE, FAIL_MSG);
            }
            queryBO.setCurrentIndex(shardIndex);
            queryBO.setTotalSelect(shardTotal - 1);
            queryBO.setDataCenter(dataScope);
            logger.info("standbyOrderSyn params is ：{}", queryBO);
            List<TradeOrderDO> tradeOrderList = payOrderService.standbyScheduleOrderQuery(queryBO);
            if (tradeOrderList == null || tradeOrderList.size() == 0) {
                logger.info("standbyOrderSyn is Empty!!, end {}", DateTimeUtils.getCurrentDateTimeStr());
                return new ReturnT(ReturnT.SUCCESS_CODE);
            }
            sendOrderData(tradeOrderList, shardIndex);
            logger.info("standbyOrderSyn is end {}", DateTimeUtils.getCurrentDateTimeStr());
            return new ReturnT(ReturnT.SUCCESS_CODE);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                logger.info("order handle fail:{}", e.getMessage());
            } else {
                logger.error("order handle fail:{}", e);
            }
            return ReturnT.FAIL;
        }
    }

    @Override
    public ReturnT<String> managerDataContractDelete(String channel, int shardIndex, int shardTotal, String param, String dataScope) {
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        String logInfo = channel + "managerDataContractDelete" + DateTimeUtils.formatLocalDateTime(localDateTime);
        logger.info("start {}", logInfo);
        if (!dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        try {
            ContractQueryBO contractQueryBO = new ContractQueryBO();
            LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
            contractQueryBO.setParam(param);
            contractQueryBO.setTotalSelect(shardTotal);
            contractQueryBO.setCreateDate(DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
            contractQueryBO.setParam(StringUtils.isNotBlank(param) ? param : TOTAL);
            contractQueryBO.setCurrentIndex(shardIndex);
            contractQueryBO.setTotalSelect(shardTotal - 1);
            contractQueryBO.setContractWay(channel);
            contractQueryBO.setDataCenter(dataScope);
            List<ContractDeleteDO> contractDeleteDOList = contractDeleteService.findContractDeleteScheduleList(contractQueryBO);
            if (contractDeleteDOList == null || contractDeleteDOList.size() == 0) {
                logger.info("contractDeleteInfo is Empty!!");
                return ReturnT.SUCCESS;
            }
            logger.info("contractDeleteSyn");
            sendOrderDataContractDelete(contractDeleteDOList, shardIndex);
            logger.info("end {}", logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                logger.info("contractDelete handle fail:{}", e.getMessage());
            } else {
                logger.error("contractDelete handle fail:{}", e);
            }
            return ReturnT.FAIL;
        }
    }

    @Override
    public ReturnT<String> managerDataContract(String channel, int shardIndex, int shardTotal, String param, String dataScope) {
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        String logInfo = channel + "managerDataContract" + DateTimeUtils.formatLocalDateTime(localDateTime);
        logger.info("start {}", logInfo);
        if (!dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        try {
            ContractQueryBO contractQueryBO = new ContractQueryBO();
            String queryDate = DateTimeUtils.formatLocalDate(DateTimeUtils.getCurrentLocalDate().minusDays(1));
            contractQueryBO.setRequestDate(queryDate);
            contractQueryBO.setStartTime(DateTimeUtils.formatLocalDateTime(localDateTime.minusHours(24)));
            contractQueryBO.setEndTime(DateTimeUtils.formatLocalDateTime(localDateTime.minusHours(23)));
            contractQueryBO.setParam(StringUtils.isNotBlank(param) ? param : TOTAL);
            contractQueryBO.setContractWay(channel);
            contractQueryBO.setCurrentIndex(shardIndex);
            contractQueryBO.setTotalSelect(shardTotal - 1);
            contractQueryBO.setDataCenter(dataScope);
            List<ContractWithholdDO> contractWithholdDOList = contractWithholdService.findContractScheduleList(contractQueryBO);
            if (contractWithholdDOList == null || contractWithholdDOList.size() == 0) {
                logger.info("contractInfo is Empty!!");
                return ReturnT.SUCCESS;
            }
            logger.info("contractSyn");
            sendOrderDataContract(contractWithholdDOList, shardIndex);
            logger.info("end {}", logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                logger.info("contract handle fail:{}", e.getMessage());
            } else {
                logger.error("contract handle fail:{}", e);
            }
            return ReturnT.FAIL;
        }
    }

    @Override
    public ReturnT<String> managerDataHourContract(String channel, int shardIndex, int shardTotal, String param, String dataScope) {
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        String logInfo = channel + "managerDataHourContract" + DateTimeUtils.formatLocalDateTime(localDateTime);
        logger.info("start {}", logInfo);
        if (!dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
        try {
            ContractQueryBO contractQueryBO = new ContractQueryBO();
            String queryDate = DateTimeUtils.formatLocalDate(DateTimeUtils.getCurrentLocalDate().minusDays(1));
            contractQueryBO.setRequestDate(queryDate);
            contractQueryBO.setCreateDate(DateTimeUtils.formatLocalDateTime(localDateTime.minusHours(2)));
            contractQueryBO.setParam(StringUtils.isNotBlank(param) ? param : TOTAL);
            contractQueryBO.setContractWay(channel);
            contractQueryBO.setCurrentIndex(shardIndex);
            contractQueryBO.setTotalSelect(shardTotal - 1);
            contractQueryBO.setDataCenter(dataScope);
            List<ContractWithholdDO> contractWithholdDOList = contractWithholdService.findContractScheduleList(contractQueryBO);
            if (contractWithholdDOList == null || contractWithholdDOList.size() == 0) {
                logger.info("contractInfo is Empty!!");
                return ReturnT.SUCCESS;
            }
            logger.info("contractSyn");
            sendOrderDataContract(contractWithholdDOList, shardIndex);
            logger.info("end {}", logInfo);
            return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                logger.info("contract handle fail:{}", e.getMessage());
            } else {
                logger.error("contract handle fail:{}", e);
            }
            return ReturnT.FAIL;
        }
    }


    @Override
    public ReturnT<String> managerDataMasterOrder(int shardIndex, int shardTotal, String param) {
        LocalDateTime localDateTime = DateTimeUtils.parseLocalDateTime(DateTimeUtils.getCurrentDateTimeStr());
        String logInfo = "queryMasterOrder" + DateTimeUtils.formatLocalDateTime(localDateTime);
        logger.info("start {}", logInfo);
        if (!dataInspect(shardIndex, shardTotal)) {
            return ReturnT.FAIL;
        }
//        调度参数：指定参数按逗号分割：1000,订单号,日期
        String[] params = param.split(",");
        String total = "";
        String tradeOrderNo = "";
        String requestDate = "";
        if (JudgeUtils.isNotEmpty(params) && params.length > 1) {
            total = params[0];
            tradeOrderNo = params[1];
            requestDate = params[2];
        }

        try {
            // 1、查询所有服务bean为“SubMixPayWritterService”的所有子商户
            List<SubMerchantBO> subMerchantBOS = subMerchantService.queryMixPaySubMerchant();
            if (JudgeUtils.isEmpty(subMerchantBOS)) {
                logger.info("MixSubMerchantBOS is Empty!!");
                return ReturnT.SUCCESS;
            }
            //遍历子商户，每个子商户只查一次，每次查询条数从外面参数中返回
            for (SubMerchantBO subMerchantBO : subMerchantBOS) {
                //  2、查询结算表：为当前子商户，结算日期大于等于昨天，且主订单为空的结算表订单
                TradeSettlementQueryBO queryBO = new TradeSettlementQueryBO();
                LocalDate localDate = DateTimeUtils.parseLocalDate(DateTimeUtils.getCurrentDateStr());
                queryBO.setRequestDate(StringUtils.isNotBlank(requestDate) ? requestDate : DateTimeUtils.formatLocalDate(localDate.minusDays(1)));
                queryBO.setOrderDate(StringUtils.isNotBlank(requestDate) ? requestDate : DateTimeUtils.formatLocalDate(localDate));
                queryBO.setSubMerchantNo(subMerchantBO.getSubMerchantNo());
                queryBO.setCurrentIndex(shardIndex);
                queryBO.setTotalSelect(shardTotal - 1);
                queryBO.setTotal(StringUtils.isNotBlank(total) ? total : TOTAL);
                queryBO.setTradeOrderNo(StringUtils.isNotBlank(tradeOrderNo) ? tradeOrderNo : null);
                List<TradeSettlementBO> settlementBOS = settlementService.queryMixPayOrder(queryBO);
                if (JudgeUtils.isEmpty(settlementBOS)) {
                    logger.info(subMerchantBO.getSubMerchantNo() + " MixPayOrder Order is Empty!!");
                }
                logger.info("masterOrderQuerySyn");
                //  3、用这笔数据，调用“统一收银台商户订单号获取”接口
                sendMixPayOrderData(settlementBOS, shardIndex);
                logger.info("end {}", DateTimeUtils.formatLocalDateTime(localDateTime));
                return new ReturnT(ReturnT.SUCCESS_CODE, logInfo);
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                logger.info("order handle fail:{}", e.getMessage());
            } else {
                logger.error("order handle fail:{}", e);
            }
            return ReturnT.FAIL;
        }
    }

    private boolean checkParams(FapOrderQueryBO queryBO, String param) {
        // 必填日期
        if (JudgeUtils.isBlank(param)) {
            return false;
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.BASIC_ISO_DATE;
        FapOrderQueryBO fapOrderQueryBO = JSONObject.parseObject(param, FapOrderQueryBO.class);
        BeanUtils.copyProperties(fapOrderQueryBO, queryBO);
        if (JudgeUtils.isBlankAny(queryBO.getOrderDate(), queryBO.getTotal())) {
            return false;
        }
        try {
            LocalDate.parse(queryBO.getOrderDate(), dateTimeFormatter);
        } catch (Exception e) {
            return false;
        }
        if (JudgeUtils.isNotBlank(queryBO.getAimProductCode())) {
            queryBO.setAimArray(queryBO.getAimProductCode().split(","));
        }
        if (JudgeUtils.isNotBlank(queryBO.getPayWayCode())) {
            queryBO.setPayWayArray(queryBO.getPayWayCode().split(","));
        }
        if (JudgeUtils.isNotBlank(queryBO.getPayProductCode())) {
            queryBO.setPayArray(queryBO.getPayProductCode().split(","));
        }
        if (!(JudgeUtils.isNotBlankAll(queryBO.getStartTime(), queryBO.getEndTime())
                || JudgeUtils.isBlankAll(queryBO.getStartTime(), queryBO.getEndTime()))) {
            return false;
        }
        return true;
    }

    /**
     * 数据发送上游(解约数据)
     *
     * @param contractDeleteDOList
     * @param shardIndex
     */
    private void sendOrderDataContractDelete(List<ContractDeleteDO> contractDeleteDOList, int shardIndex) {
        for (ContractDeleteDO contractDeleteDO : contractDeleteDOList) {
            logger.info("当前分片: {}, 处理对象: {}", shardIndex, contractDeleteDO.getContractCode());
            ContractQueryBO contractQuery = new ContractQueryBO();
            contractQuery.setContractDeleteDO(contractDeleteDO);
            try {
                com.cmpay.lemon.common.utils.BeanUtils.copyProperties(contractQuery, contractDeleteDO);
                contractQuery.setAppId(contractDeleteDO.getAppId());
                contractQuery.setScene(contractDeleteDO.getContractScene());
                contractQuery.setPlanId(contractDeleteDO.getPlanId());
                contractQuery.setContractCode(contractDeleteDO.getContractCode());
                contractQuery.setContractId(contractDeleteDO.getContractId());
                contractQuery.setContractWay(contractDeleteDO.getContractWay());
                contractSynService.contractDeleteQuery(contractQuery);
            } catch (Exception e) {
                if (e instanceof BusinessException) {
                    logger.info("contractDeleteInfo:{},error msg:{}", contractQuery.getContractCode(), e.getMessage());
                } else {
                    logger.error("contractDeleteInfo:{},error msg:{}", contractQuery.getContractCode(), e);
                }
            }
        }
    }

    /**
     * 数据发送上游(签约数据)
     *
     * @param contractWithholdDOList
     * @param shardIndex
     */
    private void sendOrderDataContract(List<ContractWithholdDO> contractWithholdDOList, int shardIndex) {
        for (ContractWithholdDO contractWithholdDO : contractWithholdDOList) {
            logger.info("当前分片: {}, 处理对象: {}", shardIndex, contractWithholdDO.getContractCode());
            ContractQueryBO contractQuery = new ContractQueryBO();
            contractQuery.setContractWithholdDO(contractWithholdDO);
            try {
                com.cmpay.lemon.common.utils.BeanUtils.copyProperties(contractQuery, contractWithholdDO);
                contractQuery.setAppId(contractWithholdDO.getAppId());
                contractQuery.setPlanId(contractWithholdDO.getPlanId());
                contractQuery.setContractCode(contractWithholdDO.getContractCode());
                contractQuery.setContractId(contractWithholdDO.getContractId());
                contractQuery.setContractWay(contractWithholdDO.getContractWay());
                contractQuery.setScene(contractWithholdDO.getContractScene());
                contractSynService.contractQuery(contractQuery);
            } catch (Exception e) {
                if (e instanceof BusinessException) {
                    logger.info("contractInfo:{},error msg:{}", contractQuery.getContractCode(), e.getMessage());
                } else {
                    logger.error("contractInfo:{},error msg:{}", contractQuery.getContractCode(), e);
                }
            }
        }
    }
    /**
     * 数据发送上游(签约扣款订单)
     *
     * @param settlementBOList
     * @param shardIndex
     */
    private void sendMixPayOrderData(List<TradeSettlementBO> settlementBOList, int shardIndex) {
        for (TradeSettlementBO settlementBO : settlementBOList) {
            logger.info("当前分片: {}, 处理对象: {}", shardIndex, settlementBO.getTradeOrderNo());
            try {
                masterOrderService.orderQueryHandler(settlementBO);
            } catch (Exception e) {
                if (e instanceof BusinessException) {
                    logger.info("settlementBO:{},error msg:{}", settlementBO.getTradeOrderNo(), e.getMessage());
                } else {
                    logger.error("settlementBO:{},error msg:{}", settlementBO.getTradeOrderNo(), e);
                }
            }
        }
    }



}
