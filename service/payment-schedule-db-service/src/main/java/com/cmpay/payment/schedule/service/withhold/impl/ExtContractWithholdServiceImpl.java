package com.cmpay.payment.schedule.service.withhold.impl;

import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.dao.BaseDao;

import com.cmpay.payment.constant.Constants;
import com.cmpay.payment.constant.MsgCodeEnum;
import com.cmpay.payment.schedule.bo.withhold.ContractQueryBO;
import com.cmpay.payment.schedule.bo.withhold.ContractWithholdBO;
import com.cmpay.payment.schedule.dao.withhold.IContractWithholdExtDao;
import com.cmpay.payment.schedule.entity.withhold.ContractWithholdDO;
import com.cmpay.payment.schedule.entity.withhold.ContractWithholdDOKey;
import com.cmpay.payment.schedule.service.config.IExtParamInfoService;
import com.cmpay.payment.schedule.service.withhold.IExtContractWithholdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/07/27
 */
@Service
@Slf4j
public class ExtContractWithholdServiceImpl implements IExtContractWithholdService {
    @Autowired
    IContractWithholdExtDao contractWithholdDao;
    @Autowired
    IExtParamInfoService paramInfoService;


    @Override
    public ContractWithholdDO findByContractCode(ContractWithholdBO contractBO) {
        ContractWithholdDO contractDO = new ContractWithholdDO();
        contractDO.setContractCode(contractBO.getContractCode());
        List<ContractWithholdDO> list = contractWithholdDao.find(contractDO);
        if (list.size() > Constants.ZERO_NUMBER) {
            return list.get(Constants.ZERO_NUMBER);
        }
        return null;
    }


    @Override
    public void updateContractInfo(ContractWithholdDO contractWithholdDO) {
        if (contractWithholdDao.update(contractWithholdDO) != 1) {
            BusinessException.throwBusinessException(MsgCodeEnum.UPDATE_CONTRACT_STATUS_ERROR);
        }
    }

    @Override
    public List<ContractWithholdDO> findContractScheduleList(ContractQueryBO contractQueryBO) {
        if (JudgeUtils.isBlank(contractQueryBO.getCreateDate())) {
            return contractWithholdDao.findContractWaitList(contractQueryBO);
        }
        return contractWithholdDao.findHourContractWaitList(contractQueryBO);
    }


    @Override
    public List<ContractWithholdDO> findContractCleanList(ContractQueryBO contractQueryBO) {
        return contractWithholdDao.findContractCleanList(contractQueryBO);
    }

    @Override
    public List<ContractWithholdDO> findTerminationContractCleanList(ContractQueryBO contractQueryBO) {
        return contractWithholdDao.findTerminationContractCleanList(contractQueryBO);
    }

    @Override
    public void deleteBatch(List<String> contractCodeList) {
        contractWithholdDao.deleteBatch(contractCodeList);
    }

    @Override
    public BaseDao getDao() {
        return null;
    }

    @Override
    public ContractWithholdDO get(ContractWithholdDOKey id) {
        return contractWithholdDao.get(id);
    }

    @Override
    public List<ContractWithholdDO> find(ContractWithholdDO entity) {
        return contractWithholdDao.find(entity);
    }

    @Override
    public int insert(ContractWithholdDO entity) {
        return contractWithholdDao.insert(entity);
    }

    @Override
    public int update(ContractWithholdDO entity) {
        return contractWithholdDao.update(entity);
    }

    @Override
    public int delete(ContractWithholdDOKey id) {
        return contractWithholdDao.delete(id);
    }
}
