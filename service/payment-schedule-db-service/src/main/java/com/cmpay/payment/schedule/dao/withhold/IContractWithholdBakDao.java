/*
 * @ClassName IContractWithholdBakDao
 * @Description 
 * @version 1.0
 * @Date 2023-07-17 16:31:48
 */
package com.cmpay.payment.schedule.dao.withhold;

import com.cmpay.lemon.framework.dao.BaseDao;
import com.cmpay.payment.schedule.entity.withhold.ContractWithholdBakDO;
import com.cmpay.payment.schedule.entity.withhold.ContractWithholdBakDOKey;
import com.cmpay.payment.schedule.entity.withhold.ContractWithholdDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface IContractWithholdBakDao extends BaseDao<ContractWithholdBakDO, ContractWithholdBakDOKey> {
    /**
     * 协议批量插入备份表
     * @param list
     */
    void insertBatch(List<ContractWithholdDO> list);
}