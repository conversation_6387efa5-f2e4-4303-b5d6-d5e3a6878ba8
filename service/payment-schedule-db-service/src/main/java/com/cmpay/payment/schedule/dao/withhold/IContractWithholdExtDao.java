/*
 * @ClassName IContractWithholdDao
 * @Description
 * @version 1.0
 * @Date 2024-05-27 14:20:07
 */
package com.cmpay.payment.schedule.dao.withhold;

import com.cmpay.payment.schedule.bo.withhold.ContractQueryBO;
import com.cmpay.payment.schedule.entity.withhold.ContractWithholdDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface IContractWithholdExtDao extends IContractWithholdDao {


    /**
     * 查询待签约信息
     *
     * @param queryBO
     * @return
     */
    List<ContractWithholdDO> findContractWaitList(ContractQueryBO queryBO);

    /**
     * 查询待签约信息
     *
     * @param queryBO
     * @return
     */
    List<ContractWithholdDO> findHourContractWaitList(ContractQueryBO queryBO);

    /**
     * 查询需要备份清除的数据
     *
     * @param queryBO
     * @return
     */
    List<ContractWithholdDO> findContractCleanList(ContractQueryBO queryBO);


    void deleteBatch(List<String> list);


    List<ContractWithholdDO> findTerminationContractCleanList(ContractQueryBO contractQueryBO);
}