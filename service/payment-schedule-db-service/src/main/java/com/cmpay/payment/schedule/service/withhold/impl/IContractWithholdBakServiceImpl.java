package com.cmpay.payment.schedule.service.withhold.impl;

import com.cmpay.payment.schedule.dao.withhold.IContractWithholdBakDao;
import com.cmpay.payment.schedule.entity.withhold.ContractWithholdDO;
import com.cmpay.payment.schedule.service.withhold.IContractWithholdBakService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/1 17:25
 */
@Service
public class IContractWithholdBakServiceImpl implements IContractWithholdBakService {
    @Autowired
    IContractWithholdBakDao contractWithholdBakDao;

    @Override
    public void insertBatch(List<ContractWithholdDO> result) {
        contractWithholdBakDao.insertBatch(result );
    }
}
