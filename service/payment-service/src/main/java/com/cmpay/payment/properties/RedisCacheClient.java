package com.cmpay.payment.properties;

import com.cmpay.lemonframework.redis.ConditionalOnRedisContext;
import com.cmpay.lemonframework.redis.RedisContext;
import com.cmpay.lemonframework.redis.annotation.RedisClient;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * <AUTHOR>
 * Created on 2021/2/26
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass(RedisContext.class)
@RedisClient(prefix = "lemon.cache.redis", name = "cacheRedisContext",configuration = {RedisCacheClient.RedisCacheConfiguration.class}, registerRedisTemplate = true, beanNames = {"cacheRedisTemplate"})
public class RedisCacheClient {

    @Bean
    public IntegrationPaymentCacheRedis integrationPaymentCacheRedis(RedisContext redisContext) {
        return new IntegrationPaymentCacheRedis(redisContext.getRedisTemplate("cacheRedisContext", String.class, Object.class));
    }

    @ConditionalOnRedisContext
    static class RedisCacheConfiguration {
        @Bean
        public RedisTemplate<String,Object> cacheRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
            RedisTemplate template = new RedisTemplate();
            template.setConnectionFactory(redisConnectionFactory);
            ObjectMapper mapper = new ObjectMapper(); //jsr310,localeDate
            mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
            mapper.registerModule(new JavaTimeModule());
            mapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
            Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
            jackson2JsonRedisSerializer.setObjectMapper(mapper);
            RedisSerializer<String> stringSerializer = new StringRedisSerializer();
            template.setKeySerializer(stringSerializer);
            template.setHashKeySerializer(stringSerializer);
            template.setValueSerializer(jackson2JsonRedisSerializer);
            template.setHashValueSerializer(jackson2JsonRedisSerializer);
            template.afterPropertiesSet();
            return template;
        }
    }

}
