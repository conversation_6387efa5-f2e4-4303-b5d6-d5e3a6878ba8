package com.cmpay.payment.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Created on 2020/06/16
 *
 * @author: huang_yh1
 */
@ConfigurationProperties(prefix = "payment.protocol")
@Configuration
public class ProtocolProperties {

    /**
     * 3des key
     */
    private String tripleDes;
    /**
     * 数字货币缴费商户号
     */
    private String phoneMerchantNo;

    public String getPhoneMerchantNo() {
        return phoneMerchantNo;
    }

    public void setPhoneMerchantNo(String phoneMerchantNo) {
        this.phoneMerchantNo = phoneMerchantNo;
    }

    public String getTripleDes() {
        return tripleDes;
    }

    public void setTripleDes(String tripleDes) {
        this.tripleDes = tripleDes;
    }
}
