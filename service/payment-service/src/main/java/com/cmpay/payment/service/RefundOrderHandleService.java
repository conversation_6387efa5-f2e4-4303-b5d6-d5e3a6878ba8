package com.cmpay.payment.service;

import com.cmpay.payment.bo.RefundOrderQueryBO;
import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;

/**
 * @author： PengAnHai
 * @date： 2024-08-22
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
public interface RefundOrderHandleService {

    /**
     * 设置支付订单状态
     *
     * @param refundQueryBO 退款订单查询业务对象
     * @return 更新后的退款订单查询业务对象
     */
    RefundOrderQueryBO setPayOrderStatus(RefundOrderQueryBO refundQueryBO);

    /**
     * 退款失败设置支付订单状态
     *
     * @param refundQueryBO 退款订单查询业务对象
     * @return 更新后的退款订单查询业务对象
     */
    RefundOrderQueryBO setPayOrderStatusRefFail(RefundOrderQueryBO refundQueryBO);

    /**
     * 设置退款订单的手续费金额
     *
     * @param refundQueryBO 退款订单查询业务对象
     * @return 更新后的退款订单查询业务对象
     */
    RefundOrderQueryBO setOrderFeeAmount(RefundOrderQueryBO refundQueryBO);


    /**
     * 通知商户退款结果
     *
     * @param refundOrderDO 退款订单数据对象
     * @return 通知是否成功
     */
    boolean notifyMerchant(RefundOrderDO refundOrderDO);
}
