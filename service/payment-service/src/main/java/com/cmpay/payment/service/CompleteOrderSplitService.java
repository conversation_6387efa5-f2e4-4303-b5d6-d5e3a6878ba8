package com.cmpay.payment.service;

import com.cmpay.payment.entity.RefundOrderDO;
import com.cmpay.payment.entity.TradeOrderDO;

/**
 * @date 2023-11-25 09:43
 * 分账订单接收通知，或主动查询，使支付成功，或退款成功，此service提供分账功能
 * <AUTHOR>
 * @Version 1.0
 */
public interface CompleteOrderSplitService {

    /**
     * 分账订单服务费计算
     *
     * @param tradeOrderDO
     * @return
     */
    TradeOrderDO splitOrderFeeCalculate(TradeOrderDO tradeOrderDO);


    /**
     * 计算分账订单退款服务费
     *
     * @param refundOrderDO
     * @param tradeOrderDO
     * @return
     */
    RefundOrderDO splitOrderRefundFee(RefundOrderDO refundOrderDO, TradeOrderDO tradeOrderDO);


    /**
     * 明确退款失败，则将金额自增回来
     *
     * @param refundOrderDO
     * @param tradeOrderDO
     * @return
     */
    RefundOrderDO refundFailIncrement(RefundOrderDO refundOrderDO, TradeOrderDO tradeOrderDO);

    /**
     * 检查是否是分账订单
     *
     * @param refundOrderDO
     * @param tradeOrderDO
     * @return
     */
    boolean checkSplitOrder(RefundOrderDO refundOrderDO, TradeOrderDO tradeOrderDO);
}
