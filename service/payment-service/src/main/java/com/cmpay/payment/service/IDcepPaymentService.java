package com.cmpay.payment.service;

import com.cmpay.payment.bo.ProtocolPaymentBO;

/**
 * <AUTHOR>
 * @date 2022/7/5
 */
public interface IDcepPaymentService {

    /**
     * 预下单
     *
     * @param protocolPaymentBO
     * @return
     */
    void protocolPrePayment(ProtocolPaymentBO protocolPaymentBO);

    /**
     * 预下单查询
     *
     * @param protocolPaymentBO
     * @return
     */
    void protocolPrePaymentQuery(ProtocolPaymentBO protocolPaymentBO);

    /**
     * 免密协议支付
     *
     * @param protocolPaymentBO
     * @return
     */
    void protocolPayment(ProtocolPaymentBO protocolPaymentBO);

    /**
     * 免密协议支付
     *
     * @param protocolPaymentBO
     * @return
     */
    void protocolMmpayPayment(ProtocolPaymentBO protocolPaymentBO);

    /**
     * 统一支付
     *
     * @param protocolPaymentBO
     */
    void unifiedPay(ProtocolPaymentBO protocolPaymentBO);
}
