package com.cmpay.payment.service;

import com.cmpay.payment.bo.RefundBO;

/**
 * Created on 2018/11/30
 *
 * @author: sun_zhh
 */
public interface RefundVerifyService {

    /**
     * 校验原支付订单状态，是否允许退款
     * @param refundBO
     * @return RefundBO
     */
    void verifyOrderStatus(RefundBO refundBO);

    /**
     * 退款订单存在，报错
     * 原支付订单不存在，报错
     * @param refundBO
     */
    void verifyRefundOrderExist(RefundBO refundBO);

    /**
     * 判断原支付订单，是否超过退款时间
     * @param refundBO
     */
    void verifyRefundTimeout(RefundBO refundBO);

    /**
     * 判断原支付订单状态，是否允许退款
     * @param refundBO
     */
    void verifyOrigOrderStatus(RefundBO refundBO);

    /**
     * 退款请求，校验金额
     * @param refundBO
     * @return RefundBO
     */
    void verifyRefundAmount(RefundBO refundBO);

    void subMerchantRefundConfine(RefundBO refundBO);
}
