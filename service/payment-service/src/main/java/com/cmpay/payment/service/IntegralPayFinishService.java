package com.cmpay.payment.service;

import com.cmpay.payment.bo.IntegralPayCommitRecordBO;
import com.cmpay.payment.bo.IntegralPayFinishBO;

/**
 * <AUTHOR>
 * @Date 2021-8-31 0031 10:52
 */
public interface IntegralPayFinishService {
    /**
     * 积分发货
     */
    IntegralPayFinishBO payFinish(IntegralPayFinishBO integralPayFinishBO);
    /**
     * 积分完结提交消费记录
     */
    IntegralPayCommitRecordBO payCommitRecord(IntegralPayCommitRecordBO integralPayCommitRecordBO);
}
