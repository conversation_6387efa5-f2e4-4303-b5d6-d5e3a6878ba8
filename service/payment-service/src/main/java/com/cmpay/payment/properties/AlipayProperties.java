package com.cmpay.payment.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Created on 2018/12/21
 *
 * @author: wulinfeng
 */
@Configuration
@ConfigurationProperties(prefix = "alipay")
public class AlipayProperties {

    /**
     * 通知地址
     */
    private String notifyUrl;
    /**
     * 回调地址
     */
    private String returnUrl;
    /**
     * 退款回调地址
     */
    private String refundUrl;
    /**
     * Appid
     */
    private String appid;
    /**
     * pid
     */
    private String pid;
    /**
     * 卖家ID
     */
    private String sellerId;
    /**
     * rsa2私钥
     */
    private String rsa2Private;
    /**
     * 版本
     */
    private String version;
    /**
     * 支付宝签约通知地址
     */
    private String contractNotifyUrl;
    /**
     * 支付宝解约通知地址
     */
    private String contractDeleteNotifyUrl;
    /**
     * 支付宝签约回调地址
     */
    private String returnPageContract;


    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getReturnUrl() {
        return returnUrl;
    }

    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

    public String getRefundUrl() {
        return refundUrl;
    }

    public void setRefundUrl(String refundUrl) {
        this.refundUrl = refundUrl;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getRsa2Private() {
        return rsa2Private;
    }

    public void setRsa2Private(String rsa2Private) {
        this.rsa2Private = rsa2Private;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getContractNotifyUrl() {
        return contractNotifyUrl;
    }

    public void setContractNotifyUrl(String contractNotifyUrl) {
        this.contractNotifyUrl = contractNotifyUrl;
    }

    public String getContractDeleteNotifyUrl() {
        return contractDeleteNotifyUrl;
    }

    public void setContractDeleteNotifyUrl(String contractDeleteNotifyUrl) {
        this.contractDeleteNotifyUrl = contractDeleteNotifyUrl;
    }

    public String getReturnPageContract() {
        return returnPageContract;
    }

    public void setReturnPageContract(String returnPageContract) {
        this.returnPageContract = returnPageContract;
    }
}
