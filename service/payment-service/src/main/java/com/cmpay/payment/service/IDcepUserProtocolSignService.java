package com.cmpay.payment.service;

import com.cmpay.payment.bo.ProtocolSignInTokenBO;
import com.cmpay.payment.bo.protocol.ProtocolAbolishBO;
import com.cmpay.payment.bo.protocol.ProtocolApplyBO;
import com.cmpay.payment.bo.protocol.ProtocolQueryBO;
import com.cmpay.payment.bo.protocol.ProtocolSignBO;

/**
 * Created on 2020/5/12
 *
 * @author: huang_yh1
 */
public interface IDcepUserProtocolSignService {

    /**
     * 免密协议申请
     *
     * @param protocolApplyBO
     * @return
     */
    void protocolApply(ProtocolApplyBO protocolApplyBO);

    /**
     * 免密协议签约
     *
     * @param protocolSignBO
     * @return
     */
    void protocolSign(ProtocolSignBO protocolSignBO);

    /**
     * 免密协议解约
     *
     * @param protocolAbolishBO
     * @return
     */
    void protocolAbolish(ProtocolAbolishBO protocolAbolishBO);

    /**
     * 免密协议查询
     *
     * @param protocolQueryBO
     * @return
     */
    void protocolQuery(ProtocolQueryBO protocolQueryBO);

    /**
     * wap免密协议查询
     *
     * @param protocolQueryBO
     * @return
     */
    void protocolWapQuery(ProtocolQueryBO protocolQueryBO);

    /**
     * 获取单点登录手机号
     *
     * @param protocolSignInTokenBO
     * @return
     */
    void getMobileNo(ProtocolSignInTokenBO protocolSignInTokenBO);
    /**
     * 免密协议查询
     * @param protocolQueryBO
     * @return
     */
    ProtocolQueryBO protocolInfoQuery(ProtocolQueryBO protocolQueryBO);
}
