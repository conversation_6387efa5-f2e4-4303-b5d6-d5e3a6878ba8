package com.cmpay.payment.service;

import com.cmpay.payment.bo.config.SubOrderInfoBO;
import com.cmpay.payment.bo.config.SubOrderInfoExtBO;

import java.math.BigDecimal;
import java.util.List;

/**
 * @date 2023-11-24 10:40
 * 分账参数解析，分账支付接口、订单成功分账公用
 * <AUTHOR>
 * @Version 1.0
 */
public interface SubOrderInfosResolveService {

    /**
     * 解析分账参数，获取子订单List
     * 可能会解析失败，需处理好抛出的异常
     *
     * @param subOrderInfos 分账参数
     * @return 子订单List
     */
    List<SubOrderInfoBO> resolve(String subOrderInfos);


    List<SubOrderInfoExtBO> resolveExt(String subOrderInfos);


    /**
     * 解析分账参数，并计算所有子订单金额之和
     *
     * @param subOrderInfos 分账参数
     * @return 所有子订单金额之和
     */
    BigDecimal sumSubOrderAmount(String subOrderInfos);

}
