package com.cmpay.payment.service;

import com.cmpay.payment.bo.config.SubMerchantRefreshAmountBO;


/**
 * @date 2023-11-23 11:58
 * 子商户金额校验操作
 * <AUTHOR>
 * @Version 1.0
 */
public interface SubMerchantIncomeService {

    /**
     * 确认改子商户是否允许退款
     * @param refreshAmountBO
     */
    void refundConfine(SubMerchantRefreshAmountBO refreshAmountBO);

    /**
     * 明确退款失败，当日退款总金额是进行自减去
     * @param refreshAmountBO
     * @return
     */
    boolean refundFailSubtractAmount(SubMerchantRefreshAmountBO refreshAmountBO);
}
