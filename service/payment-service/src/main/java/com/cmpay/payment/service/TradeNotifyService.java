package com.cmpay.payment.service;

import com.cmpay.payment.bo.RefundNotifyBO;
import com.cmpay.payment.bo.RefundOrderCloneBO;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.bo.TradeOrderAndNotifyBO;
import com.cmpay.payment.bo.data.AlipayRiskgoNotifyBO;
import com.cmpay.payment.entity.TradeOrderDO;

/**
 * Created on 2018/12/04
 *
 * @author: li_zhen
 */
public interface TradeNotifyService{
    TradeNotifyBO backendNotify(TradeNotifyBO tradeNotifyBO);

    void tradeNotify(TradeNotifyBO tradeNotifyBO);

    TradeOrderAndNotifyBO backendQueryOrder(TradeOrderDO tradeOrderDO);

    /**
     * 退款结果通知
     * @param refundNotifyBO
     */
    void handleRefundResultNotification(RefundNotifyBO refundNotifyBO);

    /**
     * 退款结果通知
     * @param refundNotifyBO
     */
    void refundResultNotify(RefundNotifyBO refundNotifyBO);

    /**
     * 支付宝风险防控通知
     * @param alipayRiskgoNotifyBO
     */
    void backendalipayRiskgoNotify(AlipayRiskgoNotifyBO alipayRiskgoNotifyBO);

    /**
     * 处理退款通知
     * @param refundOrderCloneBO
     * @return
     */
    boolean handleRefundNotify (RefundOrderCloneBO refundOrderCloneBO);

    /**
     * 运管手动下发通知
     * @param tradeNotifyBO
     * @return
     */
    boolean managerOrderNotify(TradeNotifyBO tradeNotifyBO);

}
