package com.cmpay.payment.service;

import com.cmpay.payment.bo.IntegralBalanceQueryBO;
import com.cmpay.payment.bo.IntegralGoodsQueryBO;

/**
 * Created on 2024/01/24
 *
 * @author: wu_cm
 */
public interface IntegralQueryService {
    /**
     * 积分余额查询
     */
    IntegralBalanceQueryBO integralBalanceQuery(IntegralBalanceQueryBO balanceQueryBO);

    /**
     * 积分商品列表查询
     */
    IntegralGoodsQueryBO integralGoogsQuery(IntegralGoodsQueryBO integralFinishQueryBO);
}
