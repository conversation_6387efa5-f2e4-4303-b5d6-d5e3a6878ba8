package com.cmpay.payment.service;

import com.cmpay.payment.bo.UserLoginBO;

/**
 * <AUTHOR>
 * @date ：Created in 2021/1/20 15:08
 * @description ：短信验证码
 */
public interface SystemSmsCodeService {
    /**
     * 下发短信验证码
     * @param userLoginBO
     * @return
     */
    UserLoginBO lssueVerificationCode(UserLoginBO userLoginBO);

    /**
     * 校验短信验证码
     * @param userLoginBO
     * @return
     */
    UserLoginBO checkIdentifyCode(UserLoginBO userLoginBO);

    /**
     * 删除短信验证码缓存
     * @param userLoginBO
     */
    void deleteIdentifyCode(UserLoginBO userLoginBO);

    /**
     * 校验短信验证码
     * @param userLoginBO
     * @param smsCode
     * @return
     */
    boolean checkSmsCode(UserLoginBO userLoginBO, String smsCode);

}
