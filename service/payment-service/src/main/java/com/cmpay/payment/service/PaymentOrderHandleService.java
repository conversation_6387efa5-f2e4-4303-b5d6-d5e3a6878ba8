package com.cmpay.payment.service;

import com.cmpay.payment.entity.TradeOrderDO;

/**
 * @author： PengAnHai
 * @date： 2024-08-21
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
public interface PaymentOrderHandleService {

    /**
     * 设置支付订单的费率信息
     *
     * @param tradeOrder 交易订单对象
     * @return 更新后的交易订单对象
     */
    TradeOrderDO setPayOrderRate(TradeOrderDO tradeOrder);

    /**
     * 通知商户支付结果
     *
     * @param tradeOrder 交易订单对象
     * @return 通知结果，成功返回true
     */
    boolean notifyMerchant(TradeOrderDO tradeOrder);


}
