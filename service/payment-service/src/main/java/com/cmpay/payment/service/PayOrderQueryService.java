package com.cmpay.payment.service;

import com.cmpay.payment.bo.PayNotifyBO;
import com.cmpay.payment.bo.PayOrderQueryBO;

/**
 * Created on 2018/11/30
 *
 * @author: sun_zhh
 */
public interface PayOrderQueryService {

    /**
     * 支付查询
     *
     * @param payOrderQueryBO 支付结果查询业务对象
     * @return PayOrderQueryBO 支付结果查询业务对象
     */
    PayOrderQueryBO payOrderQuery(PayOrderQueryBO payOrderQueryBO);

    /**
     * 页面通知查询
     *
     * @param payOrderQueryBO 支付结果查询业务对象
     * @return PayNotifyBO 页面通知业务对象
     */
    PayNotifyBO pageNotifyQuery(PayOrderQueryBO payOrderQueryBO);
}
