package com.cmpay.payment.service;

import com.cmpay.payment.bo.PayOrderBO;
import com.cmpay.payment.bo.RefundBO;

/**
 * Created on 2018/11/30
 *
 * @author: sun_zhh
 */
public interface SplitPayOrderService {

    /**
     * 支付交易
     *
     * @param payOrderBO
     */
    PayOrderBO payOrder(PayOrderBO payOrderBO);

    /**
     * 退款交易
     * @param refundBO
     * @return RefundBO
     */
    RefundBO refund(RefundBO refundBO);
}
