package com.cmpay.payment.service;

import com.cmpay.payment.bo.RefundBO;
import com.cmpay.payment.entity.TradeOrderDO;

/**
 * @date 2025-04-19 19:21
 * <AUTHOR>
 * @Version 1.0
 */
public interface RefundOrderExtFunctionService {

    /**
     * 请求参数不为空检查
     *
     * @param refundBO
     * @return
     */
    void inputCannotEmptyCheck(RefundBO refundBO);

    /**
     * 日期校验
     *
     * @param payDate,mchRfOrderNo
     */
    void checkDate(String payDate);

    /**
     * 检查订单是否超过退款有效期
     *
     * @param originalPayTradeOrderDO
     * @return
     */
    void CheckExceedRefundDeadline(TradeOrderDO originalPayTradeOrderDO,RefundBO refundParam);
}
