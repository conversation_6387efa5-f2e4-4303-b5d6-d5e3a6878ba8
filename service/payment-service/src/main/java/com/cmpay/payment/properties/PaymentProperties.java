package com.cmpay.payment.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Created on 2018/12/21
 *
 * @author: wulinfeng
 */
@Configuration
@ConfigurationProperties(prefix = "payment.application")
public class PaymentProperties {

    String fileUrlConfig;
    String wapUrl;

    String secureKey;
    String secureIndex;

    public String getSecureKey() {
        return secureKey;
    }

    public void setSecureKey(String secureKey) {
        this.secureKey = secureKey;
    }

    public String getSecureIndex() {
        return secureIndex;
    }

    public void setSecureIndex(String secureIndex) {
        this.secureIndex = secureIndex;
    }

    public String getFileUrlConfig() {
        return fileUrlConfig;
    }

    public String getWapUrl() {
        return wapUrl;
    }

    public void setWapUrl(String wapUrl) {
        this.wapUrl = wapUrl;
    }

    public void setFileUrlConfig(String fileUrlConfig) {
        this.fileUrlConfig = fileUrlConfig;
    }

}
