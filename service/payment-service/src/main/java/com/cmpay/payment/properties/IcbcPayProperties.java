package com.cmpay.payment.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Created on 2020/5/13
 *
 * @author: huang_yh1
 */
@ConfigurationProperties(prefix = "icbcpay")
@Configuration
public class IcbcPayProperties {

    /**
     * 商户名称
     */
    private String merchantId;

    /**
     * 文件验证公钥路径
     */
    private String filePublicKeyPath;

    /**
     * 文件校验公钥名字
     */
    private String filePublicKeyName;

    /**
     * 扫码商户号
     */
    private String microPayMerchantId;

    /**
     * 通知地址
     */
    private String notifyUrl;
    /**
     * 页面通知地址
     */
    private String pageNotifyUrl;
    /**
     * sftp ip地址
     */
    private String sftpIp;
    /**
     * 文件远程路径
     */
    private String fileRemotePath;
    /**
     * sftp 用户名
     */
    private String sftpUsername;
    /**
     * sftp端口
     */
    private Integer sftpPort;

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getMicroPayMerchantId() {
        return microPayMerchantId;
    }

    public void setMicroPayMerchantId(String microPayMerchantId) {
        this.microPayMerchantId = microPayMerchantId;
    }

    public String getFilePublicKeyPath() {
        return filePublicKeyPath;
    }

    public void setFilePublicKeyPath(String filePublicKeyPath) {
        this.filePublicKeyPath = filePublicKeyPath;
    }

    public String getFilePublicKeyName() {
        return filePublicKeyName;
    }

    public void setFilePublicKeyName(String filePublicKeyName) {
        this.filePublicKeyName = filePublicKeyName;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getPageNotifyUrl() {
        return pageNotifyUrl;
    }

    public void setPageNotifyUrl(String pageNotifyUrl) {
        this.pageNotifyUrl = pageNotifyUrl;
    }

    public String getSftpIp() {
        return sftpIp;
    }

    public void setSftpIp(String sftpIp) {
        this.sftpIp = sftpIp;
    }

    public String getFileRemotePath() {
        return fileRemotePath;
    }

    public void setFileRemotePath(String fileRemotePath) {
        this.fileRemotePath = fileRemotePath;
    }

    public Integer getSftpPort() {
        return sftpPort;
    }

    public void setSftpPort(Integer sftpPort) {
        this.sftpPort = sftpPort;
    }

    public String getSftpUsername() {
        return sftpUsername;
    }

    public void setSftpUsername(String sftpUsername) {
        this.sftpUsername = sftpUsername;
    }


}
