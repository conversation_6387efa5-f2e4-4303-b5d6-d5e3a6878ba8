package com.cmpay.payment.service;

import com.cmpay.payment.bo.SubWalletAddNewCardBO;
import com.cmpay.payment.bo.SubWalletOrderQueryBO;
import com.cmpay.payment.bo.SubWalletPayChannelQueryBO;
import com.cmpay.payment.bo.SubWalletPaymentBO;

/**
 * <AUTHOR>
 * @date 2022/5/5
 */
public interface SubWalletCashierService {

    /**
     * 子钱包订单详情查询
     *
     * @param orderQueryBO
     * @return
     */
    void queryOrderInfo(SubWalletOrderQueryBO orderQueryBO);

    /**
     * 支付方式查询
     *
     * @param payChannelQueryBO
     */
    void payChannelQuery(SubWalletPayChannelQueryBO payChannelQueryBO);

    /**
     * 子钱包支付
     *
     * @param walletPaymentBO
     */
    void subWalletPayment(SubWalletPaymentBO walletPaymentBO);

    /**
     *  子钱包添加新卡，获取scheme链接
     * @param walletAddNewCardBO
     */
    void subWalletAddNewCard(SubWalletAddNewCardBO walletAddNewCardBO);
}
