package com.cmpay.payment.client;

import com.cmpay.lemon.framework.stream.Source;
import com.cmpay.lemon.framework.stream.StreamClient;
import com.cmpay.payment.bo.TradeNotifyBO;
import com.cmpay.payment.entity.TradeOrderDO;

/**
 * Created on 2019/05/13
 *
 * <AUTHOR>
 */
@StreamClient("integration-payment-schedule")
public interface AsynPaymentClient {
    /**
     * 异步通知
     * @param tradeNotifyBO
     */
    @Source(handlerBeanName = "notifyHandler", group = "integration-payment-schedule", prefix = "mirror.")
    void asynNotify(TradeNotifyBO tradeNotifyBO);

    /**
     * 异步新增第三方支付机构订单号和支付订单号的关联
     * @param tradeOrderDO
     */
    @Source(handlerBeanName = "connectThirdOrderNoHandler", group = "integration-payment-schedule", prefix = "mirror.")
    void asynConnectThirdNo(TradeOrderDO tradeOrderDO);
}
